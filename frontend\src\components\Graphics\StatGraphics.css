/* Statistics Graphics Styles */
.stat-graphic {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  width: 100%;
  height: 100%;
  min-width: 180px;
  min-height: 100px;
  transition: all 0.3s ease;
}

.stat-graphic svg {
  width: 100%;
  height: 100%;
  max-width: 200px;
  max-height: 120px;
}

.stat-graphic:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.02);
}

.auth-page.dark .stat-graphic {
  background: rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.05);
}

.auth-page.dark .stat-graphic:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* Individual graphic styles */
.students-graphic svg {
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
}

.courses-graphic svg {
  filter: drop-shadow(0 4px 8px rgba(139, 92, 246, 0.2));
}

.success-graphic svg {
  filter: drop-shadow(0 4px 8px rgba(16, 185, 129, 0.2));
}

.rating-graphic svg {
  filter: drop-shadow(0 4px 8px rgba(245, 158, 11, 0.2));
}

/* Responsive design */
@media (max-width: 768px) {
  .stat-graphic {
    padding: 0.5rem;
  }
  
  .stat-graphic svg {
    width: 150px;
    height: 90px;
  }
}
