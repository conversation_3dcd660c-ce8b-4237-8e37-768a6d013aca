# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Frontend specific
frontend/dist/
frontend/build/
frontend/.vite/
frontend/.turbo/

# Backend specific
backend/dist/
backend/build/
backend/uploads/
backend/logs/
backend/.turbo/

# Mobile specific
mobile/dist/
mobile/.expo/
mobile/expo-env.d.ts

# Admin specific
admin/dist/
admin/build/
admin/.turbo/

# Database
database/backups/
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Docker
docker/data/
docker/logs/

# Nginx
nginx/logs/
nginx/ssl/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python (for AI components)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/

# AI Models
*.pkl
*.joblib
*.h5
*.pb
models/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Backup files
*.bak
*.backup

# Config files with sensitive data
config/production/
config/secrets/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Documentation build
docs/_build/
docs/.doctrees/

# Test files and coverage
test-results/
coverage/
*.lcov

# Security and monitoring
security-logs/
*.security.log

# Package locks (keep only package-lock.json)
yarn.lock
pnpm-lock.yaml

# Build artifacts
*.tsbuildinfo
.turbo/

# Educational Platform Specific
uploads/temp/
uploads/documents/
uploads/images/
analytics-data/
user-data/
session-data/

# API Keys and Secrets (extra protection)
*.env.*
.env*
secrets.json
api-keys.json

# Development tools
.vscode/settings.json
.vscode/launch.json
.idea/workspace.xml
.idea/tasks.xml

# Performance monitoring
performance-logs/
monitoring-data/

# Cache directories
.cache/
.tmp/
.temp/

# User uploads (keep structure, ignore content)
uploads/*
!uploads/.gitkeep
!uploads/README.md
