/* Tasks Section Styles */

.tasks-container {
  height: 100%;
  width: 100%;
  position: relative;
  background: #f9fafb;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: calc(100vh - 70px);
}

.tasks-container.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* مؤشر التحميل */
.tasks-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.tasks-container.dark .loading-spinner {
  border-color: #374151;
  border-top-color: #60a5fa;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tasks-loading p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.tasks-container.dark .tasks-loading p {
  color: #9ca3af;
}

/* منطقة المحتوى الرئيسي */
.tasks-content {
  height: calc(100vh - 120px);
  padding: 16px;
  display: flex;
  gap: 16px;
  overflow: hidden;
}

/* الجانب الأيسر - قائمة المهام */
.tasks-sidebar {
  width: 320px;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tasks-container.dark .tasks-sidebar {
  background: #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* الجانب الأيمن - منطقة التنفيذ */
.tasks-execution-area {
  flex: 1;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: calc(100vh - 150px);
}

.tasks-container.dark .tasks-execution-area {
  background: #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* منطقة الكتابة */
.tasks-input-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #f9fafb;
  padding: 20px;
  z-index: 100;
  transition: all 0.3s ease;
  border: none;
  box-shadow: none;
}

.tasks-container.dark .tasks-input-container {
  background: #0f172a;
}

.tasks-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 1200px;
  margin: 0 auto;
}

.tasks-input-field-wrapper {
  flex: 1;
  position: relative;
}

.tasks-input-field {
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 14px 18px;
  font-size: 14px;
  outline: none;
  background-color: white;
  color: #111827;
  transition: all 0.3s ease;
  font-family: inherit;
  line-height: 1.5;
  resize: none;
}

.tasks-input-field:focus {
  border-color: #f59e0b;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.tasks-container.dark .tasks-input-field {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.tasks-container.dark .tasks-input-field:focus {
  border-color: #fbbf24;
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
}

.tasks-input-field::placeholder {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.tasks-container.dark .tasks-input-field::placeholder {
  color: #6b7280;
}

.tasks-send-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.tasks-send-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.tasks-send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* هيدر قائمة المهام */
.tasks-sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.tasks-container.dark .tasks-sidebar-header {
  border-bottom-color: #374151;
}

.tasks-sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.tasks-container.dark .tasks-sidebar-title {
  color: #f9fafb;
}

/* إحصائيات المهام */
.tasks-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.tasks-stat-card {
  flex: 1;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 12px;
  padding: 12px;
  text-align: center;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tasks-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.tasks-stat-card.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.tasks-stat-card.completed:hover {
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.tasks-stat-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tasks-stat-label {
  font-size: 11px;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  line-height: 1.2;
}

/* تأثير التحديث */
.tasks-stat-card.updating {
  animation: statUpdate 0.6s ease-in-out;
}

@keyframes statUpdate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
  100% {
    transform: scale(1);
  }
}

.tasks-stat-card.completed.updating {
  animation: statUpdateCompleted 0.6s ease-in-out;
}

@keyframes statUpdateCompleted {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
  100% {
    transform: scale(1);
  }
}

/* الفلاتر */
.tasks-filters {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.tasks-filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tasks-filter-label {
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tasks-container.dark .tasks-filter-label {
  color: #9ca3af;
}

.tasks-filter-select {
  padding: 8px 10px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  color: #374151;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tasks-filter-select:focus {
  outline: none;
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);
}

.tasks-container.dark .tasks-filter-select {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.tasks-container.dark .tasks-filter-select:focus {
  border-color: #fbbf24;
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.1);
}

/* قائمة المهام */
.tasks-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 8px 12px 12px;
  max-height: calc(100vh - 320px);
}

.tasks-list::-webkit-scrollbar {
  width: 6px;
}

.tasks-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  margin: 4px 0;
}

.tasks-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.4) 0%, rgba(217, 119, 6, 0.4) 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.tasks-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.7) 0%, rgba(217, 119, 6, 0.7) 100%);
}

.tasks-container.dark .tasks-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.tasks-container.dark .tasks-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.4) 0%, rgba(245, 158, 11, 0.4) 100%);
}

.tasks-container.dark .tasks-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.7) 0%, rgba(245, 158, 11, 0.7) 100%);
}

/* بطاقة المهمة */
.task-card {
  background: #f9fafb;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border-left: 4px solid transparent;
  margin-bottom: 12px;
  border: 1px solid #e5e7eb;
}

.task-card:hover {
  background: white;
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

.task-card.active {
  background: white;
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.15);
}

.tasks-container.dark .task-card {
  background: #374151;
  border-color: #4b5563;
}

.tasks-container.dark .task-card:hover,
.tasks-container.dark .task-card.active {
  background: #1f2937;
  border-color: #fbbf24;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.15);
}

/* ألوان حسب المستوى */
.task-card.beginner {
  border-left-color: #10b981;
}

.task-card.intermediate {
  border-left-color: #f59e0b;
}

.task-card.advanced {
  border-left-color: #ef4444;
}

.task-card.expert {
  border-left-color: #8b5cf6;
}

/* أيقونة المهمة */
.task-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
}

/* محتوى المهمة */
.task-content {
  flex: 1;
  min-width: 0;
}

/* رأس البطاقة */
.task-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.task-level {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.task-level.beginner {
  background: #dcfce7;
  color: #166534;
}

.task-level.intermediate {
  background: #fef3c7;
  color: #92400e;
}

.task-level.advanced {
  background: #fee2e2;
  color: #991b1b;
}

.task-level.expert {
  background: #f3e8ff;
  color: #6b21a8;
}

.tasks-container.dark .task-level.beginner {
  background: #064e3b;
  color: #34d399;
}

.tasks-container.dark .task-level.intermediate {
  background: #451a03;
  color: #fbbf24;
}

.tasks-container.dark .task-level.advanced {
  background: #7f1d1d;
  color: #f87171;
}

.tasks-container.dark .task-level.expert {
  background: #581c87;
  color: #c084fc;
}

.task-points {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  color: #f59e0b;
}

/* عنوان ووصف المهمة */
.task-title {
  font-size: 15px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 6px;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tasks-container.dark .task-title {
  color: #f9fafb;
}

.task-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tasks-container.dark .task-description {
  color: #9ca3af;
}

/* تفاصيل المهمة */
.task-details {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.task-detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #6b7280;
}

.tasks-container.dark .task-detail-item {
  color: #9ca3af;
}

.task-language {
  padding: 2px 6px;
  background: #f3f4f6;
  border-radius: 4px;
  font-weight: 500;
  font-size: 10px;
}

.tasks-container.dark .task-language {
  background: #374151;
}

/* معلومات إضافية */
.task-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.task-points-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  color: #f59e0b;
  font-size: 14px;
}

.task-action-area {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

/* تذييل البطاقة */
.task-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.tasks-container.dark .task-card-footer {
  border-top-color: #374151;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.tasks-container.dark .task-progress {
  color: #9ca3af;
}

.task-action-btn {
  padding: 8px 16px;
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-action-btn:hover {
  background: #d97706;
  transform: scale(1.05);
}

.task-action-btn.completed {
  background: #10b981;
}

.task-action-btn.completed:hover {
  background: #059669;
}

/* منطقة التنفيذ */
.execution-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
  flex-shrink: 0;
}

.tasks-container.dark .execution-header {
  border-bottom-color: #374151;
}

.execution-title {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 8px;
}

.tasks-container.dark .execution-title {
  color: #f9fafb;
}

.execution-subtitle {
  font-size: 14px;
  color: #6b7280;
}

.tasks-container.dark .execution-subtitle {
  color: #9ca3af;
}

.execution-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  max-height: calc(100vh - 250px);
}

.execution-content::-webkit-scrollbar {
  width: 6px;
}

.execution-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  margin: 4px 0;
}

.execution-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.4) 0%, rgba(217, 119, 6, 0.4) 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.execution-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.7) 0%, rgba(217, 119, 6, 0.7) 100%);
}

.tasks-container.dark .execution-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.tasks-container.dark .execution-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.4) 0%, rgba(245, 158, 11, 0.4) 100%);
}

.tasks-container.dark .execution-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.7) 0%, rgba(245, 158, 11, 0.7) 100%);
}

.execution-content-centered {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 100%;
}

.execution-placeholder {
  max-width: 400px;
}

.execution-placeholder-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.execution-placeholder-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #111827;
}

.tasks-container.dark .execution-placeholder-title {
  color: #f9fafb;
}

.execution-placeholder-subtitle {
  font-size: 14px;
  color: #6b7280;
}

.tasks-container.dark .execution-placeholder-subtitle {
  color: #9ca3af;
}

/* منطقة كتابة/تحرير المهمة */
.task-editor {
  padding: 20px;
}

.task-editor-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.tasks-container.dark .task-editor-header {
  border-bottom-color: #374151;
}

.task-editor-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.tasks-container.dark .task-editor-title {
  color: #f9fafb;
}

.task-editor-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 380px);
  padding-right: 8px;
}

.task-editor-content::-webkit-scrollbar {
  width: 6px;
}

.task-editor-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  margin: 4px 0;
}

.task-editor-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.4) 0%, rgba(217, 119, 6, 0.4) 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.task-editor-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.7) 0%, rgba(217, 119, 6, 0.7) 100%);
}

.tasks-container.dark .task-editor-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.tasks-container.dark .task-editor-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.4) 0%, rgba(245, 158, 11, 0.4) 100%);
}

.tasks-container.dark .task-editor-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.7) 0%, rgba(245, 158, 11, 0.7) 100%);
}

.task-editor-textarea {
  width: 100%;
  min-height: 200px;
  max-height: 400px;
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  resize: vertical;
  outline: none;
  background: #f9fafb;
  color: #111827;
  transition: all 0.3s ease;
}

.task-editor-textarea:focus {
  border-color: #f59e0b;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
  background: white;
}

.tasks-container.dark .task-editor-textarea {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.tasks-container.dark .task-editor-textarea:focus {
  border-color: #fbbf24;
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
  background: #1f2937;
}

.task-editor-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  justify-content: flex-end;
}

.task-editor-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-editor-btn.primary {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.task-editor-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.task-editor-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.task-editor-btn.secondary:hover {
  background: #e5e7eb;
}

.tasks-container.dark .task-editor-btn.secondary {
  background: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

.tasks-container.dark .task-editor-btn.secondary:hover {
  background: #4b5563;
}

/* حالة فارغة للقائمة */
.tasks-empty {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.tasks-container.dark .tasks-empty {
  color: #9ca3af;
}

.tasks-empty-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 16px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.tasks-empty-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #111827;
}

.tasks-container.dark .tasks-empty-title {
  color: #f9fafb;
}

.tasks-empty-subtitle {
  font-size: 12px;
  margin-bottom: 16px;
}
