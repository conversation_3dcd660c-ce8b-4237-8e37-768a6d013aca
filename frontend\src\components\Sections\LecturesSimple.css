/* LecturesSimple Component Styles */

.lectures-container {
  height: 100%;
  width: 100%;
  position: relative;
  background: #f9fafb;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: calc(100vh - 70px);
}

.lectures-container.dark {
  background: #0f172a;
}

/* منطقة المحتوى الرئيسي */
.lectures-content {
  height: calc(100% - 80px);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lectures-welcome {
  text-align: center;
  color: #6b7280;
  transition: color 0.3s ease;
}

.lectures-container.dark .lectures-welcome {
  color: #d1d5db;
}

.lectures-title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #111827;
  transition: color 0.3s ease;
}

.lectures-container.dark .lectures-title {
  color: #f9fafb;
}

.lectures-subtitle {
  font-size: 18px;
  color: #6b7280;
  transition: color 0.3s ease;
}

.lectures-container.dark .lectures-subtitle {
  color: #9ca3af;
}

/* مستطيل الكتابة */
.lectures-input-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #f9fafb;
  padding: 20px;
  z-index: 100;
  transition: all 0.3s ease;
  border: none;
  box-shadow: none;
}

.lectures-container.dark .lectures-input-container {
  background: #0f172a;
}

.lectures-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 1200px;
  margin: 0 auto;
}

/* منطقة الرسائل المحسنة */
.lectures-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem 1.5rem 120px 1.5rem; /* إضافة مساحة كبيرة في الأسفل */
  display: flex;
  flex-direction: column;
  gap: 1rem;
  scroll-behavior: smooth;
  max-height: calc(100vh - 200px);
  min-height: 400px;
}

.message-bubble {
  max-width: 70%;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0.75rem;
}

.message-bubble.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-bubble.ai {
  align-self: flex-start;
}

.message-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.message-avatar.user {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.message-avatar.ai {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.message-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 0.75rem 1rem;
  box-shadow:
    0 3px 15px rgba(0, 0, 0, 0.06),
    0 1px 6px rgba(0, 0, 0, 0.03);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.message-content.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.message-content.ai {
  background: rgba(255, 255, 255, 0.98);
  color: #1f2937;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.lectures-container.dark .message-content.ai {
  background: rgba(31, 41, 55, 0.98);
  color: #f9fafb;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-content:hover {
  transform: translateY(-1px);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.12),
    0 4px 12px rgba(0, 0, 0, 0.08);
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  font-weight: 400;
  letter-spacing: 0.01em;
}

.message-text strong {
  font-weight: 600;
}

.message-text code {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.lectures-container.dark .message-text code {
  background: rgba(255, 255, 255, 0.1);
}

.message-time {
  font-size: 11px;
  opacity: 0.6;
  margin-top: 0.4rem;
  font-weight: 500;
  letter-spacing: 0.02em;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.lectures-input-field-wrapper {
  flex: 1;
}

.lectures-input-field {
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 14px 18px;
  font-size: 14px;
  outline: none;
  background-color: white;
  color: #111827;
  transition: all 0.3s ease;
  font-family: inherit;
  line-height: 1.5;
  resize: none;
}

.lectures-input-field:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.lectures-container.dark .lectures-input-field {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.lectures-container.dark .lectures-input-field:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.lectures-input-field::placeholder {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.lectures-container.dark .lectures-input-field::placeholder {
  color: #6b7280;
}

/* أزرار الإجراءات */
.lectures-action-buttons {
  display: flex;
  gap: 8px;
}

.lectures-action-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* زر رفع الملفات */
.lectures-file-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.lectures-file-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

.lectures-container.dark .lectures-file-button {
  background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
}

/* زر الإرسال */
.lectures-send-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.lectures-send-button.active {
  transform: scale(1);
}

.lectures-send-button.active:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.lectures-send-button.inactive {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

.lectures-container.dark .lectures-send-button.active {
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
}

.lectures-container.dark .lectures-send-button.inactive {
  background: #374151;
  color: #6b7280;
}

/* تأثير الضغط */
.lectures-action-button:active {
  transform: scale(0.95);
}

/* إخفاء input الملف */
.lectures-file-input {
  display: none;
}

/* تأثيرات الحركة */
.lectures-input-container {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* تصميم متجاوب محسن */
@media (max-width: 768px) {
  .lectures-messages {
    padding: 1rem 1rem 100px 1rem; /* مساحة أسفل للموبايل */
    gap: 0.75rem;
  }

  .message-bubble {
    max-width: 85%;
    gap: 0.5rem;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .message-content {
    padding: 0.6rem 0.8rem;
    border-radius: 14px;
  }

  .message-text {
    font-size: 13px;
    line-height: 1.4;
  }

  .lectures-input-container {
    padding: 12px;
  }

  .lectures-input-wrapper {
    gap: 8px;
  }

  .lectures-input-field {
    padding: 12px 16px;
    font-size: 16px; /* منع التكبير في iOS */
  }

  .lectures-send-button {
    padding: 12px 16px;
    min-width: 50px;
  }

  .lectures-title {
    font-size: 24px;
  }

  .lectures-subtitle {
    font-size: 14px;
  }

  .lectures-messages::-webkit-scrollbar {
    width: 6px;
  }
}

@media (max-width: 480px) {
  .lectures-input-container {
    padding: 10px;
  }
  
  .lectures-input-field {
    padding: 10px 14px;
  }
  
  .lectures-send-button {
    padding: 10px 14px;
  }
}

/* دعم RTL */
[dir="rtl"] .lectures-input-wrapper {
  direction: rtl;
}

[dir="rtl"] .lectures-input-field {
  text-align: right;
}

/* تأثيرات إضافية للتفاعل */
.lectures-send-button.active {
  position: relative;
  overflow: hidden;
}

.lectures-send-button.active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.lectures-send-button.active:active::before {
  width: 100px;
  height: 100px;
}

/* تحسينات الأداء */
.lectures-container * {
  box-sizing: border-box;
}

.lectures-input-field,
.lectures-send-button {
  will-change: transform;
}

/* تأثيرات التحميل والرسائل */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.message-text.loading {
  opacity: 0.7;
  font-style: italic;
  color: #6b7280;
}

/* تصميم مؤشر تحليل الملفات */
.file-analysis-indicator {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #2563eb;
  animation: pulse 2s infinite;
}

.lectures-container.dark .file-analysis-indicator {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  color: #60a5fa;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* تصميم عرض الملفات المحددة */
.selected-files-container {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-file-item {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 12px;
  color: #16a34a;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.lectures-container.dark .selected-file-item {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.4);
  color: #4ade80;
}

.selected-file-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.file-remove-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  margin-left: 4px;
  font-size: 14px;
  font-weight: bold;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.file-remove-btn:hover {
  opacity: 1;
}

/* تحسينات زر رفع الملفات */
.lectures-file-button {
  position: relative;
}

.lectures-file-button:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.lectures-container.dark .message-text.loading {
  color: #9ca3af;
}

/* تحسين مظهر الرسائل */
.message-bubble.ai .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.message-bubble.user .message-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* تأثير النبض للرسائل الجديدة */
.message-bubble:last-child {
  animation: slideIn 0.3s ease-out, pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* تأثير الكتابة */
.message-bubble .message-avatar .spinning {
  color: #667eea;
}

.lectures-container.dark .message-bubble .message-avatar .spinning {
  color: #a78bfa;
}

/* تحسين التمرير ومنع الاهتزاز */
.lectures-messages {
  scroll-behavior: smooth;
  scroll-padding-bottom: 20px;
  overflow-anchor: auto;
  contain: layout style paint;
}

/* تأثير التركيز على منطقة الإدخال */
.lectures-input-container:focus-within {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.lectures-container.dark .lectures-input-container:focus-within {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* تحسين التمرير المتقدم ومنع الاهتزاز */
.lectures-messages {
  max-height: calc(100vh - 180px);
  min-height: 400px;
  padding-right: 8px;
  transform: translateZ(0); /* تفعيل تسريع الأجهزة */
  backface-visibility: hidden; /* تحسين الأداء */
  perspective: 1000px; /* تحسين التمرير */
}

/* Scrollbar مخصص جميل ومتقدم */
.lectures-messages::-webkit-scrollbar {
  width: 8px;
}

.lectures-messages::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  margin: 8px 0;
  border: 1px solid rgba(0, 0, 0, 0.02);
}

.lectures-container.dark .lectures-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.03);
}

.lectures-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(102, 126, 234, 0.8) 0%,
    rgba(118, 75, 162, 0.8) 50%,
    rgba(139, 92, 246, 0.8) 100%
  );
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.lectures-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(102, 126, 234, 1) 0%,
    rgba(118, 75, 162, 1) 50%,
    rgba(139, 92, 246, 1) 100%
  );
  transform: scaleX(1.2);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.lectures-messages::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg,
    rgba(79, 70, 229, 1) 0%,
    rgba(99, 102, 241, 1) 50%,
    rgba(124, 58, 237, 1) 100%
  );
}

/* للوضع المظلم */
.lectures-container.dark .lectures-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(167, 139, 250, 0.8) 0%,
    rgba(139, 92, 246, 0.8) 50%,
    rgba(124, 58, 237, 0.8) 100%
  );
  border-color: rgba(255, 255, 255, 0.2);
}

.lectures-container.dark .lectures-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(167, 139, 250, 1) 0%,
    rgba(139, 92, 246, 1) 50%,
    rgba(124, 58, 237, 1) 100%
  );
  box-shadow: 0 4px 16px rgba(167, 139, 250, 0.4);
}

.lectures-messages::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg,
    rgba(79, 70, 229, 1) 0%,
    rgba(99, 102, 241, 1) 50%,
    rgba(124, 58, 237, 1) 100%
  );
}

/* للمتصفحات الأخرى */
.lectures-messages {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.6) rgba(0, 0, 0, 0.05);
}

.lectures-container.dark .lectures-messages {
  scrollbar-color: rgba(167, 139, 250, 0.7) rgba(255, 255, 255, 0.05);
}

/* تأثيرات التحميل السريعة والمحسنة - مع منع الاهتزاز */
.message-text.loading {
  position: relative;
  animation: loadingPulse 1.5s ease-in-out infinite;
  min-height: 24px; /* منع تغيير الارتفاع */
  will-change: opacity; /* تحسين الأداء */
}

.message-text.loading::after {
  content: '●●●';
  animation: loadingDots 1s ease-in-out infinite;
  margin-left: 8px;
  color: rgba(139, 92, 246, 0.9);
  font-weight: bold;
  font-size: 1.2em;
  position: absolute; /* منع تأثير التخطيط */
  will-change: opacity, transform; /* تحسين الأداء */
}

@keyframes loadingPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.02); }
}

@keyframes loadingDots {
  0%, 20% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.1); }
  100% { opacity: 0; transform: scale(0.8); }
}

/* تأثير دوران سريع للأيقونة */
.spinning {
  animation: spin 0.8s linear infinite;
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.6));
}

@keyframes spin {
  from { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  to { transform: rotate(360deg) scale(1); }
}

/* تأثير توهج للرسائل الجديدة - مع منع الاهتزاز */
.message-bubble {
  contain: layout style; /* منع تأثير التخطيط */
  transform: translateZ(0); /* تفعيل تسريع الأجهزة */
}

.message-bubble.ai {
  animation: messageAppear 0.5s ease-out;
  will-change: opacity, transform; /* تحسين الأداء */
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* تحسين مظهر الرسائل ومنع الاهتزاز */
.message-content {
  position: relative;
  overflow: hidden;
  contain: layout style; /* منع تأثير التخطيط على العناصر الأخرى */
  will-change: auto; /* تحسين الأداء */
}

.message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.message-bubble:hover .message-content::before {
  transform: translateX(100%);
}

/* Chat Header */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  margin-bottom: 0.5rem;
}

.lectures-container.dark .chat-header {
  background: rgba(0, 0, 0, 0.3);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chat-icon {
  color: #8b5cf6;
  filter: drop-shadow(0 2px 4px rgba(139, 92, 246, 0.3));
}

.lectures-container.dark .chat-icon {
  color: #a78bfa;
}

.chat-title h3 {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.lectures-container.dark .chat-title h3 {
  color: white;
}

.chat-title p {
  margin: 0;
  color: #6b7280;
  font-size: 0.8rem;
}

.lectures-container.dark .chat-title p {
  color: rgba(255, 255, 255, 0.7);
}

.chat-actions {
  display: flex;
  gap: 0.5rem;
}

/* زر مسح المحادثة */
.clear-chat-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: #dc2626;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.clear-chat-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  color: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.clear-chat-btn:active {
  transform: translateY(0);
}

/* للوضع المظلم */
.lectures-container.dark .clear-chat-btn {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.4);
  color: #f87171;
}

.lectures-container.dark .clear-chat-btn:hover {
  background: rgba(239, 68, 68, 0.25);
  border-color: rgba(239, 68, 68, 0.6);
  color: #fca5a5;
}

/* مودال تأكيد المسح */
.clear-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.clear-confirm-modal {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.3s ease;
}

.lectures-container.dark .clear-confirm-modal {
  background: #1f2937;
  color: white;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.confirm-icon {
  width: 64px;
  height: 64px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: #dc2626;
}

.lectures-container.dark .confirm-icon {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.clear-confirm-modal h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
}

.lectures-container.dark .clear-confirm-modal h3 {
  color: white;
}

.clear-confirm-modal p {
  margin: 0 0 2rem 0;
  color: #6b7280;
  line-height: 1.5;
}

.lectures-container.dark .clear-confirm-modal p {
  color: #d1d5db;
}

.confirm-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.cancel-btn, .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.lectures-container.dark .cancel-btn {
  background: #374151;
  color: #d1d5db;
}

.lectures-container.dark .cancel-btn:hover {
  background: #4b5563;
}

.confirm-btn {
  background: #dc2626;
  color: white;
}

.confirm-btn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* تأثيرات إضافية للرسائل */
.message-bubble:nth-child(even) .message-content.ai {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
}

.lectures-container.dark .message-bubble:nth-child(even) .message-content.ai {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.98) 0%, rgba(17, 24, 39, 0.98) 100%);
}

/* تم نقل هذه التأثيرات لمكان آخر لتجنب التكرار */

/* تم دمج هذه التأثيرات مع التأثيرات الأساسية */

/* تأثير النبض للرسائل الجديدة */
.message-bubble:last-child {
  animation: slideIn 0.3s ease-out, pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* تحسين التمرير */
.lectures-messages {
  scroll-behavior: smooth;
  scroll-padding: 2rem;
}

/* تأثير التركيز على منطقة الرسائل */
.lectures-messages:hover {
  padding-right: 1.5rem;
}

.lectures-messages:hover::-webkit-scrollbar-thumb {
  opacity: 1;
  visibility: visible;
}

/* تحسين التمرير السلس */
.lectures-messages {
  scroll-padding-top: 20px;
  scroll-padding-bottom: 120px; /* مساحة كافية لعدم الاختفاء خلف مربع الكتابة */
}

/* تأثير الظل عند التمرير */
.lectures-messages::before {
  content: '';
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(180deg, rgba(249, 250, 251, 1) 0%, rgba(249, 250, 251, 0) 100%);
  z-index: 1;
  pointer-events: none;
}

.lectures-container.dark .lectures-messages::before {
  background: linear-gradient(180deg, rgba(17, 24, 39, 1) 0%, rgba(17, 24, 39, 0) 100%);
}

.lectures-messages::after {
  content: '';
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(0deg, rgba(249, 250, 251, 1) 0%, rgba(249, 250, 251, 0.8) 50%, rgba(249, 250, 251, 0) 100%);
  z-index: 1;
  pointer-events: none;
  margin-bottom: -40px; /* لتجنب إضافة مساحة إضافية */
}

.lectures-container.dark .lectures-messages::after {
  background: linear-gradient(0deg, rgba(15, 23, 42, 1) 0%, rgba(15, 23, 42, 0.8) 50%, rgba(15, 23, 42, 0) 100%);
}

.lectures-container.dark .lectures-messages::after {
  background: linear-gradient(0deg, rgba(17, 24, 39, 1) 0%, rgba(17, 24, 39, 0) 100%);
}
