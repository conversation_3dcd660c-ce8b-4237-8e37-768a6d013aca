/* Support Section Styles */

.support-container {
  height: 100%;
  width: 100%;
  position: relative;
  background: #f9fafb;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: calc(100vh - 70px);
}

.support-container.dark {
  background: #0f172a;
}

/* منطقة المحتوى الرئيسي */
.support-content {
  height: calc(100vh - 140px);
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  gap: 20px;
}

/* شبكة الأقسام */
.support-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  flex: 1;
}

/* بطاقة المطور */
.developer-profile {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  grid-column: 1 / -1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 30px;
  min-height: 200px;
}

.support-container.dark .developer-profile {
  background: #1f2937;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.developer-profile:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.support-container.dark .developer-profile:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* تخطيط المطور الرئيسي */
.developer-main-info {
  display: flex;
  align-items: center;
  gap: 2rem;
  text-align: right;
  width: 100%;
  flex-direction: row-reverse;
}

.support-container.rtl .developer-main-info {
  text-align: right;
  flex-direction: row-reverse;
}

.developer-info {
  flex: 1;
}

.developer-avatar {
  flex-shrink: 0;
}

/* صورة المطور */
.developer-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  font-weight: bold;
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.developer-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

/* معلومات المطور */
.developer-info {
  flex: 1;
  text-align: right;
}

.developer-name {
  font-size: 28px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 8px;
}

.support-container.dark .developer-name {
  color: #f9fafb;
}

.developer-title {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 12px;
  font-weight: 500;
}

.support-container.dark .developer-title {
  color: #9ca3af;
}

.developer-location {
  font-size: 14px;
  color: #8b5cf6;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 16px;
}

.developer-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
}

.support-container.dark .developer-description {
  color: #9ca3af;
}

/* قسم المشرف والمساعدين */
.team-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.support-container.dark .team-section {
  background: #1f2937;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.team-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.support-container.dark .team-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* صندوق المشرف */
.supervisor-box {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
}

.support-container.dark .supervisor-box {
  background: #374151;
  border-color: #4b5563;
}

.supervisor-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  background: #f0f4ff;
}

.support-container.dark .supervisor-box:hover {
  background: #4b5563;
  border-color: #8b5cf6;
}

.supervisor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.supervisor-icon {
  color: #6366f1;
  flex-shrink: 0;
}

.supervisor-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.support-container.dark .supervisor-title {
  color: #f9fafb;
}

.supervisor-name {
  font-size: 18px;
  font-weight: 700;
  color: #6366f1;
  margin: 0 0 8px 0;
}

.support-container.dark .supervisor-name {
  color: #a78bfa;
}

.supervisor-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
  font-weight: 500;
  text-align: right;
}

.support-container.dark .supervisor-description {
  color: #9ca3af;
}

/* قسم المساعدين */
.assistants-section {
  margin-top: 16px;
}

.assistants-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.support-container.dark .assistants-title {
  color: #f9fafb;
}

.assistants-icon {
  color: #6366f1;
}

.assistants-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

/* بطاقات المساعدين */
.assistant-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.15s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  border: 2px solid transparent;
}

.support-container.dark .assistant-card {
  background: #374151;
}

.assistant-card:hover {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
  transform: translateY(-2px);
}

.assistant-instagram-icon {
  color: #e1306c;
  flex-shrink: 0;
}

.assistant-card:hover .assistant-instagram-icon {
  color: white;
}

.assistant-name {
  font-size: 14px;
  font-weight: 600;
}

/* خبرات المطور */
.developer-skills {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.support-container.dark .developer-skills {
  background: #1f2937;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.developer-skills:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.support-container.dark .developer-skills:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.skills-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.support-container.dark .skills-title {
  color: #f9fafb;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.skill-item {
  background: #f8fafc;
  padding: 12px 16px;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.support-container.dark .skill-item {
  background: #374151;
  color: #d1d5db;
  border-color: #4b5563;
}

.skill-item:hover {
  background: #6366f1;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.support-container.dark .skill-item:hover {
  background: #8b5cf6;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* مربع فكرة الموقع */
.website-idea-box {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 180px;
}

.support-container.dark .website-idea-box {
  background: #1f2937;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.website-idea-box:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.2);
  border: 2px solid #22c55e;
}

.support-container.dark .website-idea-box:hover {
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
  border: 2px solid #4ade80;
}

.idea-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.support-container.dark .idea-title {
  color: #f9fafb;
}

.idea-content {
  color: #6b7280;
  line-height: 1.6;
  font-size: 14px;
}

.support-container.dark .idea-content {
  color: #9ca3af;
}

/* مربع هدف الموقع */
.website-goal-box {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 180px;
}

.support-container.dark .website-goal-box {
  background: #1f2937;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.website-goal-box:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
  border: 2px solid #3b82f6;
}

.support-container.dark .website-goal-box:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  border: 2px solid #60a5fa;
}

.goal-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.support-container.dark .goal-title {
  color: #f9fafb;
}

.goal-content {
  color: #6b7280;
  line-height: 1.6;
  font-size: 14px;
}

.support-container.dark .goal-content {
  color: #9ca3af;
}

/* طرق التواصل */
.contact-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  grid-column: 1 / -1;
}

.support-container.dark .contact-section {
  background: #1f2937;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.contact-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.support-container.dark .contact-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.contact-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.support-container.dark .contact-title {
  color: #f9fafb;
}

.contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  border: 1px solid #e5e7eb;
}

.support-container.dark .contact-method {
  background: #374151;
  border-color: #4b5563;
}

/* تأثيرات hover مخصصة لكل منصة - فقط الحواف */
.contact-method.instagram:hover {
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: 0 10px 30px rgba(225, 48, 108, 0.4) !important;
  border: 3px solid #e1306c !important;
}

.contact-method.youtube:hover {
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: 0 10px 30px rgba(255, 0, 0, 0.4) !important;
  border: 3px solid #ff0000 !important;
}

.contact-method.twitter:hover {
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4) !important;
  border: 3px solid #000000 !important;
}

.contact-method.github:hover {
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: 0 10px 30px rgba(88, 96, 105, 0.4) !important;
  border: 3px solid #586069 !important;
}

.contact-method.telegram:hover {
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: 0 10px 30px rgba(0, 136, 204, 0.4) !important;
  border: 3px solid #0088cc !important;
}

/* تأثير عام للـ contact methods */
.contact-method {
  transition: all 0.2s ease !important;
}

.contact-method:hover {
  transform: translateY(-3px) scale(1.02);
}



.contact-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.contact-icon.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.contact-icon.youtube {
  background: #ff0000;
}

.contact-icon.twitter {
  background: #000000;
}

.contact-icon.github {
  background: #333333;
}

.contact-icon.telegram {
  background: #0088cc;
}

.contact-info h4 {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 2px;
}

.contact-info p {
  font-size: 11px;
  opacity: 0.7;
  margin: 0;
}

/* تجاوب الشاشات */
@media (max-width: 1024px) {
  .support-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .support-content {
    padding: 12px;
    gap: 16px;
  }

  .assistants-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .developer-profile {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .developer-main-info {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .support-container.rtl .developer-main-info {
    flex-direction: column;
    text-align: center;
  }

  .developer-info {
    text-align: center;
  }

  .developer-info {
    text-align: center;
  }

  .developer-avatar {
    width: 100px;
    height: 100px;
    font-size: 40px;
  }

  .developer-name {
    font-size: 22px;
  }

  .developer-location {
    justify-content: center;
  }

  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-methods {
    grid-template-columns: repeat(2, 1fr);
  }

  .developer-profile,
  .developer-skills,
  .website-idea-box,
  .website-goal-box,
  .contact-section,
  .supervisor-box,
  .assistants-container {
    padding: 16px;
  }

  .idea-title,
  .goal-title,
  .contact-title,
  .skills-title,
  .supervisor-title,
  .assistants-title {
    font-size: 16px;
  }

  .supervisor-description {
    font-size: 12px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .skills-grid,
  .contact-methods {
    grid-template-columns: 1fr;
  }

  .contact-method {
    padding: 12px;
  }

  .contact-icon {
    width: 32px;
    height: 32px;
  }
}

/* بوكس الشكر والتقدير */
.thanks-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  grid-column: 1 / -1;
  margin-top: 20px;
  border: 2px solid transparent;
}

.support-container.dark .thanks-section {
  background: #1f2937;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.thanks-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
}

.support-container.dark .thanks-section:hover {
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
  border-color: #f87171;
}

.thanks-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.support-container.dark .thanks-title {
  color: #f9fafb;
}

.thanks-message {
  color: #6b7280;
  line-height: 2;
  font-size: 15px;
  margin-bottom: 20px;
  text-align: center;
  padding: 0 20px;
  white-space: pre-line;
  font-family: 'Amiri', 'Times New Roman', serif;
  font-weight: 500;
  letter-spacing: 0.3px;
  direction: rtl;
}

.support-container.dark .thanks-message {
  color: #d1d5db;
}

/* تنسيق خاص للنص الشعري */
.thanks-message {
  position: relative;
}

.thanks-message::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #6366f1, transparent);
  border-radius: 1px;
}

.thanks-message::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #6366f1, transparent);
  border-radius: 1px;
}

.thanks-telegram-links {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.thanks-telegram-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f8fafc;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  min-width: 140px;
  justify-content: center;
}

.support-container.dark .thanks-telegram-btn {
  background: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

.thanks-telegram-btn:hover {
  background: #0088cc;
  color: white;
  transform: translateY(-2px);
}

.thanks-telegram-btn svg {
  color: #0088cc;
  transition: color 0.3s ease;
}

.thanks-telegram-btn:hover svg {
  color: white;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .thanks-section {
    padding: 20px;
    margin-top: 16px;
  }

  .thanks-message {
    font-size: 13px;
    padding: 0 10px;
    line-height: 1.8;
  }

  .thanks-message::before,
  .thanks-message::after {
    width: 40px;
  }

  .thanks-telegram-links {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .thanks-telegram-btn {
    min-width: 160px;
    padding: 10px 16px;
  }
}








